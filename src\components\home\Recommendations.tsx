import moreIcon from "@/assets/icons/more.svg";
import { Separator } from "@/components/ui/separator";
import { recommendations } from "@/data/recommendations";
import { getNumberInCompactFormat } from "@/lib/utils";
import Image from "next/image";

const Recommendations = () => {
  return (
    <section className="mx-0 mt-12 sm:mx-10">
      <p className="text-lg font-bold sm:text-2xl">Recommended For You</p>
      <div className="mt-8 grid grid-cols-1 gap-x-5 gap-y-10 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {recommendations.map(recommendation => (
          <div key={recommendation.id}>
            <div className="h-[16.325rem] overflow-hidden rounded-lg">
              <Image src={recommendation.image} alt={`${recommendation.messageTitle} image`} className="h-full w-full object-cover" />
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between">
                <p className="line-clamp-1 text-[1.125rem]">{recommendation.messageTitle}</p>
                <Image src={moreIcon} alt="More icon" width={18} height={18} className="cursor-pointer" />
              </div>
              <div className="flex items-center justify-between text-sm font-light text-[#FFFFFFB8]">
                <div className="flex items-center gap-2">
                  <p>{`${getNumberInCompactFormat(recommendation.numberOfListens)} listen${recommendation.numberOfListens > 1 ? "s" : ""}`}</p>
                  <Separator orientation="vertical" className="h-3 border-[#FFFFFFB8]" />
                  <p>{recommendation.date}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Image
                  src={recommendation.minister.image}
                  alt={`${recommendation.minister.name} image`}
                  className="h-4 w-4 rounded-full border border-white object-cover"
                />
                <p className="text-sm font-light text-[#FFFFFFB8]">{recommendation.minister.name}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Recommendations;
