import DownloadIcon from "@/components/icons/DownloadPopular";
import PlayIcon from "@/components/icons/Play";
import { popularMessages } from "@/data/popular";
import { getNumberInCompactFormat } from "@/lib/utils";
import Image from "next/image";
import { CgMore, CgMoreVertical } from "react-icons/cg";

const Popular = () => {
  return (
    <section className="mx-0 mt-12 sm:mx-10">
      <p className="text-lg font-bold sm:text-2xl">Popular</p>
      <div className="mt-8 space-y-2">
        {popularMessages.map((message, i) => (
          <div
            className={`flex items-center justify-between ${i % 2 === 0 ? "bg-gradient-to-r from-[#FFFFFF03] via-[#FFFFFF1A] to-[#FFFFFF00]" : ""} py-4 text-[#B3B3B3]`}
            key={message.id}
          >
            <div className="flex w-96 items-center gap-4">
              <div className="relative">
                <Image
                  src={message.image}
                  alt={`${message.messageTitle} image`}
                  className="min-h-32 w-[6.25rem] min-w-25 rounded-[8px] object-cover sm:min-h-auto lg:h-[4.7rem]"
                />

                <div className="absolute top-12 left-8 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-black sm:hidden">
                  <PlayIcon />
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-md font-semibold text-white md:text-lg">{message.messageTitle}</p>
                <p className="text-xs font-light md:text-sm">{message.minister}</p>

                <p className="block w-48 text-sm md:hidden">{message.meetingName}</p>
                <p className="block text-sm md:hidden">{`${getNumberInCompactFormat(message.numberOfListens)} listen${message.numberOfListens > 1 ? "s" : ""}`}</p>
                <p className="block text-xs md:hidden">{message.duration}</p>
              </div>
            </div>

            <div className="hidden w-60 flex-col justify-between gap-2 text-sm md:flex lg:w-104 lg:flex-row lg:items-center lg:text-base">
              <p className="xl:w-[40%]">{message.meetingName}</p>
              <p className="xl:w-[30%]">{`${getNumberInCompactFormat(message.numberOfListens)} listen${message.numberOfListens > 1 ? "s" : ""}`}</p>
              <p className="xl:w-[30%]">{message.duration}</p>
            </div>

            <div className="flex items-center gap-5 self-start pt-5 sm:self-auto sm:pt-0">
              <div className="hidden h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#D6D6D61A] sm:flex">
                <PlayIcon />
              </div>
              <div className="hidden h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#D6D6D61A] xl:flex">
                <DownloadIcon />
              </div>

              <CgMoreVertical className="block h-[0.875rem] w-[0.875rem] sm:hidden" />

              <div className="hidden h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#D6D6D61A] sm:flex">
                <CgMore className="h-[0.875rem] w-[0.875rem]" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Popular;
