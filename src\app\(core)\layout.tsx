import BottomNavbar from "@/components/common/BottomNavbar";
import Sidebar from "@/components/common/Sidebar";
import TopBar from "@/components/common/TopBar";
import React from "react";

const CoreLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="relative pb-(--nav-height) lg:pb-0">
      <Sidebar />
      <div className="ml-2 lg:ml-[5rem]">
        <TopBar />
        <div className="mt-20 min-h-dvh">{children}</div>
      </div>
      <BottomNavbar />
    </div>
  );
};

export default CoreLayout;
