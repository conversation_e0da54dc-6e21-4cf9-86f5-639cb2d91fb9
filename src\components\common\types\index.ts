import { SVGProps, ReactNode } from "react";

export type IconProps = SVGProps<SVGSVGElement> & {
  isActive?: boolean;
};

export type ReusableCarouselProps<T> = {
  title: string;
  items: T[];
  renderItem: (item: T) => ReactNode;
  itemWidth: string;
};

export type CarouselItemData = {
  id: string | number;
};

export type SearchBarProps = {
  className?: string;
  placeholder?: string;
  hasMicrophone?: boolean;
};
