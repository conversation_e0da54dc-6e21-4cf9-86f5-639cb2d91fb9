import backIcon from "@/assets/icons/arrow-left.svg";
import rabbaiConversationsIcon from "@/assets/icons/rabbai-conversations.svg";
import SearchInput from "@/components/common/SearchInput";
import Image from "next/image";
import Link from "next/link";
import { ConversationHistoryProps } from "./types";

const ConversationHistory = ({ conversations }: ConversationHistoryProps) => {
  return (
    <div className="hidden min-h-dvh border-r border-[#D0D5DD29] p-[2rem] lg:block lg:w-[25rem] xl:w-[27rem]">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/rabbai">
            <Image src={backIcon} alt="Conversation Icon" width={22} height={22} />
          </Link>
          <p>Ask RabbAI</p>
        </div>
        <div className="grid size-12 place-items-center rounded-full bg-white">
          <Image src={rabbaiConversationsIcon} alt="Conversation Icon" width={22} height={22} />
        </div>
      </div>
      <div className="mt-6">
        <SearchInput className="w-full rounded-[50px]" placeholder="Search Conversation" />
        <div className="mt-6 space-y-6">
          {conversations.map(conversation => (
            <div key={conversation.id}>
              <p>{conversation.date}</p>
              <div className="mt-2 space-y-4">
                {conversation.messages.map(message => (
                  <div className="h-[3.4rem] rounded-[6px] bg-[#FFFFFF0F] px-4 py-2" key={message.id}>
                    <p className="truncate text-[0.875rem]">{message.title}</p>
                    <p className="text-[0.75rem] font-light text-[#FFFFFF73]">{message.created_at}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ConversationHistory;
