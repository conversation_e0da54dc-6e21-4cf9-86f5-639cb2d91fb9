import backIcon from "@/assets/icons/arrow-left.svg";
import rabbaiConversationsIcon from "@/assets/icons/rabbai-conversations.svg";
import SearchInput from "@/components/common/SearchInput";
import Image from "next/image";
import Link from "next/link";
import { X } from "lucide-react";
import { ConversationHistoryProps } from "./types";

const ConversationHistory = ({ conversations, isHistoryOpen, setIsHistoryOpen }: ConversationHistoryProps) => {
  return (
    <>
      {/* Mobile Overlay */}
      {isHistoryOpen && <div className="fixed inset-0 z-40 bg-black/50 lg:hidden" onClick={() => setIsHistoryOpen(false)} />}

      {/* Conversation History Panel */}
      <div
        className={`fixed top-0 left-0 z-50 h-dvh w-[20rem] transform border-r border-[#D0D5DD29] bg-[#020213] p-[2rem] transition-transform duration-300 ease-in-out ${isHistoryOpen ? "translate-x-0" : "-translate-x-full"} lg:relative lg:z-auto lg:block lg:w-[25rem] lg:translate-x-0 lg:bg-transparent xl:w-[27rem]`}
      >
        {/* Mobile Close Button */}
        <button
          type="button"
          onClick={() => setIsHistoryOpen(false)}
          className="absolute top-4 right-4 rounded-full p-2 text-white hover:bg-white/10 lg:hidden"
          aria-label="Close conversation history"
        >
          <X size={20} />
        </button>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/rabbai">
              <Image src={backIcon} alt="Conversation Icon" width={22} height={22} />
            </Link>
            <p>Ask RabbAI</p>
          </div>
          <div className="grid size-12 place-items-center rounded-full bg-white">
            <Image src={rabbaiConversationsIcon} alt="Conversation Icon" width={22} height={22} />
          </div>
        </div>
        <div className="mt-6">
          <SearchInput className="w-full rounded-[50px]" placeholder="Search Conversation" />
          <div className="mt-6 space-y-6">
            {conversations.map(conversation => (
              <div key={conversation.id}>
                <p>{conversation.date}</p>
                <div className="mt-2 space-y-4">
                  {conversation.messages.map(message => (
                    <div className="h-[3.4rem] rounded-[6px] bg-[#FFFFFF0F] px-4 py-2" key={message.id}>
                      <p className="truncate text-[0.875rem]">{message.title}</p>
                      <p className="text-[0.75rem] font-light text-[#FFFFFF73]">{message.created_at}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default ConversationHistory;
