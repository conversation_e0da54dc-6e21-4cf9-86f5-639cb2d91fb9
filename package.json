{"name": "web-v3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,json}'", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@tailwindcss/postcss": "^4.1.4", "axios": "^1.7.9", "change-case": "^5.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "husky": "^9.1.7", "lucide-react": "^0.474.0", "next": "15.1.6", "nuqs": "^2.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.1.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.5.1", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5"}, "pnpm": {"ignoredBuiltDependencies": ["sharp"]}}