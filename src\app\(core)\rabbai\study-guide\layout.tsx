"use client";
import ConversationHistory from "@/components/rabbai/ConversationHistory";
import { studyGuideConversations } from "@/data/rabbai";
import { useState } from "react";
import { HiOutlineMenuAlt2 } from "react-icons/hi";

const StudyGuideLayout = ({ children }: { children: React.ReactNode }) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState<boolean>(false);

  return (
    <div className="relative">
      {/* Mobile Menu Button */}
      <div className="flex items-center justify-between p-4 lg:hidden">
        <button
          type="button"
          onClick={() => setIsHistoryOpen(true)}
          className="flex items-center gap-2 rounded-lg p-2 text-white hover:bg-white/10"
          aria-label="Open conversation history"
        >
          <HiOutlineMenuAlt2 className="size-6" />
          <span className="text-sm">Conversations</span>
        </button>
      </div>

      {/* Main Layout */}
      <div className="lg:flex lg:gap-[2rem] xl:gap-[8rem]">
        <ConversationHistory conversations={studyGuideConversations} isHistoryOpen={isHistoryOpen} setIsHistoryOpen={setIsHistoryOpen} />
        <div className="flex-1 px-4 lg:px-0">{children}</div>
      </div>
    </div>
  );
};

export default StudyGuideLayout;
